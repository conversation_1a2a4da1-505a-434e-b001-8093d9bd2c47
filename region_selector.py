# -*- coding: utf-8 -*-
"""
屏幕区域选择模块
允许用户通过拖拽在屏幕上选择区域，获取坐标信息
"""

import tkinter as tk
from tkinter import messagebox
import logging
from typing import Tuple, Optional, Callable
import time
from PIL import Image, ImageTk
import mss
import cv2
import numpy as np

class MagnifierWindow:
    """鼠标跟随放大镜窗口"""
    
    def __init__(self, parent_root):
        """
        初始化放大镜窗口
        
        Args:
            parent_root: 父窗口引用
        """
        self.logger = logging.getLogger(__name__)
        self.parent_root = parent_root
        self.magnifier_window = None
        self.magnifier_canvas = None
        self.is_visible = False
        self.magnification = 4  # 4倍放大
        self.capture_size = 50  # 捕获50x50像素区域
        self.window_size = 200  # 显示窗口200x200像素
        self.offset_x = 30  # 鼠标右下角偏移
        self.offset_y = 30
        self.last_x = 0
        self.last_y = 0
        
        # 性能优化相关
        self.last_update_time = 0
        self.update_interval = 0.05  # 最小更新间隔50ms (20fps)
        self.min_move_distance = 3  # 最小移动距离3像素才更新
        self.image_cache = {}  # 图像缓存
        self.cache_size_limit = 10  # 缓存大小限制
        
    def create_magnifier(self):
        """创建放大镜窗口"""
        if self.magnifier_window is not None:
            return
            
        try:
            # 创建置顶小窗口
            self.magnifier_window = tk.Toplevel(self.parent_root)
            self.magnifier_window.title("放大镜")
            self.magnifier_window.geometry(f"{self.window_size}x{self.window_size}")
            
            # 设置窗口属性
            self.magnifier_window.attributes('-topmost', True)
            self.magnifier_window.overrideredirect(True)  # 无边框
            self.magnifier_window.configure(bg='black')
            
            # 创建画布
            self.magnifier_canvas = tk.Canvas(
                self.magnifier_window,
                width=self.window_size,
                height=self.window_size,
                highlightthickness=2,
                highlightbackground='red',
                bg='black'
            )
            self.magnifier_canvas.pack()
            
            # 初始隐藏
            self.magnifier_window.withdraw()
            self.logger.info("放大镜窗口创建成功")
            
        except Exception as e:
            self.logger.error(f"创建放大镜窗口失败: {str(e)}")
    
    def show_magnifier(self, x: int, y: int):
        """
        显示放大镜并更新位置
        
        Args:
            x, y: 鼠标当前位置
        """
        if not self.is_visible or self.magnifier_window is None:
            return
        
        # 性能优化：检查更新频率和移动距离
        current_time = time.time()
        if current_time - self.last_update_time < self.update_interval:
            return
            
        # 检查鼠标移动距离
        move_distance = ((x - self.last_x) ** 2 + (y - self.last_y) ** 2) ** 0.5
        if move_distance < self.min_move_distance and self.last_x != 0:
            return
            
        try:
            # 更新窗口位置（避免遮挡鼠标）
            screen_width = self.parent_root.winfo_screenwidth()
            screen_height = self.parent_root.winfo_screenheight()
            
            # 计算放大镜窗口位置
            mag_x = x + self.offset_x
            mag_y = y + self.offset_y
            
            # 边界检查，确保窗口不超出屏幕
            if mag_x + self.window_size > screen_width:
                mag_x = x - self.window_size - self.offset_x
            if mag_y + self.window_size > screen_height:
                mag_y = y - self.window_size - self.offset_y
                
            # 更新窗口位置
            self.magnifier_window.geometry(f"{self.window_size}x{self.window_size}+{mag_x}+{mag_y}")
            
            # 捕获并显示放大图像
            self._update_magnifier_content(x, y)
            
            # 显示窗口
            self.magnifier_window.deiconify()
            
            # 更新时间和位置记录
            self.last_update_time = current_time
            self.last_x = x
            self.last_y = y
            
        except Exception as e:
            self.logger.error(f"显示放大镜失败: {str(e)}")
    
    def hide_magnifier(self):
        """隐藏放大镜"""
        if self.magnifier_window:
            self.magnifier_window.withdraw()
    
    def toggle_visibility(self):
        """切换放大镜显示状态"""
        self.is_visible = not self.is_visible
        if not self.is_visible:
            self.hide_magnifier()
    
    def set_visible(self, visible: bool):
        """设置放大镜可见性"""
        self.is_visible = visible
        if not visible:
            self.hide_magnifier()
    
    def _update_magnifier_content(self, center_x: int, center_y: int):
        """
        更新放大镜内容
        
        Args:
            center_x, center_y: 放大区域中心点坐标
        """
        try:
            # 计算捕获区域
            half_size = self.capture_size // 2
            x1 = max(0, center_x - half_size)
            y1 = max(0, center_y - half_size)
            
            # 创建缓存键（基于区域位置）
            cache_key = f"{x1}_{y1}"
            
            # 检查缓存
            if cache_key in self.image_cache:
                photo = self.image_cache[cache_key]
            else:
                # 使用mss捕获指定区域
                with mss.mss() as sct:
                    # 定义捕获区域
                    capture_area = {
                        'left': x1,
                        'top': y1,
                        'width': self.capture_size,
                        'height': self.capture_size
                    }
                    
                    # 截取区域
                    screenshot = sct.grab(capture_area)
                    
                    # 转换为PIL Image
                    pil_image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                    
                    # 4倍放大
                    enlarged_image = pil_image.resize(
                        (self.window_size, self.window_size),
                        Image.NEAREST  # 使用最近邻插值保持像素清晰
                    )
                    
                    # 转换为tkinter显示格式
                    photo = ImageTk.PhotoImage(enlarged_image)
                    
                    # 添加到缓存
                    self._add_to_cache(cache_key, photo)
            
            # 清除画布并显示新图像
            self.magnifier_canvas.delete("all")
            self.magnifier_canvas.create_image(
                self.window_size // 2,
                self.window_size // 2,
                image=photo
            )
            
            # 绘制十字准线
            self._draw_crosshair()
            
            # 显示坐标信息
            self._draw_coordinates(center_x, center_y)
            
            # 保持图像引用避免被垃圾回收
            self.magnifier_canvas.image = photo
                
        except Exception as e:
            self.logger.error(f"更新放大镜内容失败: {str(e)}")
    
    def _add_to_cache(self, key: str, image):
        """
        添加图像到缓存
        
        Args:
            key: 缓存键
            image: 图像对象
        """
        # 如果缓存已满，删除最旧的条目
        if len(self.image_cache) >= self.cache_size_limit:
            # 删除第一个（最旧的）条目
            oldest_key = next(iter(self.image_cache))
            del self.image_cache[oldest_key]
        
        # 添加新图像
        self.image_cache[key] = image
    
    def _draw_crosshair(self):
        """在放大镜中心绘制十字准线"""
        center = self.window_size // 2
        line_length = 20
        
        # 绘制十字准线
        self.magnifier_canvas.create_line(
            center - line_length, center,
            center + line_length, center,
            fill='red', width=2, tags='crosshair'
        )
        self.magnifier_canvas.create_line(
            center, center - line_length,
            center, center + line_length,
            fill='red', width=2, tags='crosshair'
        )
        
        # 中心点
        self.magnifier_canvas.create_oval(
            center - 2, center - 2,
            center + 2, center + 2,
            fill='red', outline='white', width=1, tags='crosshair'
        )
    
    def _draw_coordinates(self, x: int, y: int):
        """
        在放大镜中显示坐标信息
        
        Args:
            x, y: 当前鼠标坐标
        """
        coord_text = f"({x}, {y})"
        self.magnifier_canvas.create_text(
            self.window_size // 2, 15,
            text=coord_text,
            fill='yellow',
            font=('Arial', 12, 'bold'),
            tags='coordinates'
        )
    
    def destroy(self):
        """销毁放大镜窗口"""
        if self.magnifier_window:
            try:
                # 清理缓存
                self.image_cache.clear()
                
                # 销毁窗口
                self.magnifier_window.destroy()
                self.magnifier_window = None
                self.magnifier_canvas = None
            except Exception as e:
                self.logger.error(f"销毁放大镜窗口失败: {str(e)}")

class RegionSelector:
    """屏幕区域选择器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        初始化区域选择器
        
        Args:
            callback: 选择完成后的回调函数，接收(x, y, width, height)参数
        """
        self.logger = logging.getLogger(__name__)
        self.callback = callback
        self.root = None
        self.canvas = None
        self.start_x = 0
        self.start_y = 0
        self.current_x = 0
        self.current_y = 0
        self.rect_id = None
        self.is_selecting = False
        self.screenshot = None
        self.selected_region = None
        self.magnifier = None  # 放大镜实例
        self.shift_pressed = False  # Shift键状态
        
    def start_selection(self) -> Optional[Tuple[int, int, int, int]]:
        """
        开始区域选择
        
        Returns:
            选择的区域坐标 (x, y, width, height) 或 None
        """
        try:
            # 获取屏幕截图
            self.screenshot = self._capture_screen()
            if self.screenshot is None:
                messagebox.showerror("错误", "无法获取屏幕截图")
                return None
            
            # 创建全屏选择窗口
            self._create_selection_window()
            
            # 等待用户选择
            self.root.mainloop()
            
            return self.selected_region
            
        except Exception as e:
            self.logger.error(f"区域选择失败: {str(e)}")
            messagebox.showerror("错误", f"区域选择失败: {str(e)}")
            return None
    
    def _capture_screen(self) -> Optional[Image.Image]:
        """
        捕获屏幕截图
        
        Returns:
            PIL图像对象或None
        """
        try:
            with mss.mss() as sct:
                # 获取主显示器信息
                monitor = sct.monitors[1]  # monitors[0]是所有显示器的总和
                
                # 截取整个屏幕
                screenshot = sct.grab(monitor)
                
                # 转换为PIL Image
                pil_image = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                
                self.logger.info(f"屏幕截图尺寸: {pil_image.size}")
                return pil_image
                
        except Exception as e:
            self.logger.error(f"屏幕截图失败: {str(e)}")
            return None
    
    def _create_selection_window(self):
        """创建选择窗口"""
        # 创建顶级窗口
        self.root = tk.Toplevel()
        self.root.title("区域选择 - 拖拽选择多空资金区域")
        
        # 设置窗口属性
        self.root.attributes('-topmost', True)  # 置顶
        self.root.attributes('-fullscreen', True)  # 全屏
        self.root.attributes('-alpha', 0.3)  # 设置窗口透明度
        self.root.configure(background='black')
        
        # 绑定键盘事件
        self.root.bind('<Escape>', self._cancel_selection)  # ESC键取消
        self.root.bind('<KeyPress-Shift_L>', self._on_shift_press)  # 左Shift键
        self.root.bind('<KeyPress-Shift_R>', self._on_shift_press)  # 右Shift键
        self.root.bind('<KeyRelease-Shift_L>', self._on_shift_release)  # 左Shift键释放
        self.root.bind('<KeyRelease-Shift_R>', self._on_shift_release)  # 右Shift键释放
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root,
            highlightthickness=0,
            cursor="crosshair"
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self._on_mouse_down)
        self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_mouse_up)
        self.canvas.bind('<Motion>', self._on_mouse_move)  # 鼠标悬停事件
        
        # 创建放大镜
        self.magnifier = MagnifierWindow(self.root)
        self.magnifier.create_magnifier()
        
        # 添加提示文本
        self._add_instructions()
        
        # 使窗口获得焦点以接收键盘事件
        self.root.focus_force()
        self.canvas.focus_set()
    
    def _display_screenshot(self):
        """在画布上显示截图（透明模式）"""
        try:
            # 获取画布尺寸
            self.root.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            # 不显示截图，只创建透明画布以供绘制选择框
            # 这样用户可以直接看到下层的指南针软件界面
            self.logger.info(f"创建透明选择画布，尺寸: {canvas_width}x{canvas_height}")
            
        except Exception as e:
            self.logger.error(f"创建透明画布失败: {str(e)}")
    
    def _add_instructions(self):
        """添加操作说明"""
        # 延迟到画布尺寸确定后添加说明
        self.root.after(100, self._delayed_add_instructions)
    
    def _delayed_add_instructions(self):
        """延迟添加操作说明"""
        instructions = [
            "拖拽鼠标选择多空资金数据区域",
            "按住 Shift 键显示放大镜",
            "选择完成后松开鼠标",
            "按 ESC 键取消选择"
        ]
        
        # 获取实际画布尺寸
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        y_offset = 50
        for instruction in instructions:
            self.canvas.create_text(
                canvas_width // 2, y_offset,
                text=instruction,
                fill='yellow',
                font=('Arial', 16, 'bold'),
                tags='instructions'
            )
            y_offset += 35
    
    def _on_shift_press(self, event):
        """Shift键按下事件"""
        if not self.shift_pressed:
            self.shift_pressed = True
            if self.magnifier:
                self.magnifier.set_visible(True)
                # 如果有当前鼠标位置，立即显示放大镜
                if hasattr(self, 'current_mouse_x') and hasattr(self, 'current_mouse_y'):
                    self.magnifier.show_magnifier(self.current_mouse_x, self.current_mouse_y)
    
    def _on_shift_release(self, event):
        """Shift键释放事件"""
        self.shift_pressed = False
        if self.magnifier:
            self.magnifier.set_visible(False)
    
    def _on_mouse_move(self, event):
        """鼠标移动事件（不按住按钮）"""
        # 记录当前鼠标位置
        self.current_mouse_x = event.x
        self.current_mouse_y = event.y
        
        # 如果Shift键被按下且放大镜存在，显示放大镜
        if self.shift_pressed and self.magnifier:
            self.magnifier.show_magnifier(event.x, event.y)
    
    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        self.start_x = event.x
        self.start_y = event.y
        self.current_x = event.x
        self.current_y = event.y
        self.is_selecting = True
        
        # 删除之前的选择框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
    
    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.is_selecting:
            return
            
        self.current_x = event.x
        self.current_y = event.y
        
        # 更新鼠标位置记录
        self.current_mouse_x = event.x
        self.current_mouse_y = event.y
        
        # 删除之前的选择框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
        
        # 绘制新的选择框（加粗红色边框，更明显）
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y,
            self.current_x, self.current_y,
            outline='red', width=3, tags='selection'
        )
        
        # 显示当前选择的坐标信息
        self._update_coordinate_display()
        
        # 如果Shift键被按下且放大镜存在，显示放大镜
        if self.shift_pressed and self.magnifier:
            self.magnifier.show_magnifier(event.x, event.y)
    
    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if not self.is_selecting:
            return
            
        self.current_x = event.x
        self.current_y = event.y
        self.is_selecting = False
        
        # 计算选择区域
        x = min(self.start_x, self.current_x)
        y = min(self.start_y, self.current_y)
        width = abs(self.current_x - self.start_x)
        height = abs(self.current_y - self.start_y)
        
        # 检查选择区域大小
        if width < 10 or height < 10:
            messagebox.showwarning("警告", "选择区域太小，请重新选择")
            return
        
        # 转换为实际屏幕坐标
        self.selected_region = self._convert_to_screen_coordinates(x, y, width, height)
        
        # 直接调用回调函数，无需确认
        if self.callback and self.selected_region:
            self.callback(*self.selected_region)
        
        # 关闭选择窗口
        self._close_selection()
    
    def _convert_to_screen_coordinates(self, x: int, y: int, width: int, height: int) -> Tuple[int, int, int, int]:
        """
        将画布坐标转换为绝对屏幕坐标
        
        Args:
            x, y, width, height: 画布坐标
            
        Returns:
            绝对屏幕坐标 (x, y, width, height)
        """
        # 在透明全屏模式下，画布坐标即为绝对屏幕坐标
        # 直接返回原始坐标作为绝对屏幕坐标
        return (x, y, width, height)
    
    def _update_coordinate_display(self):
        """更新坐标显示"""
        if not self.is_selecting:
            return
            
        # 删除之前的坐标显示
        self.canvas.delete('coordinates')
        
        # 计算当前选择区域
        x = min(self.start_x, self.current_x)
        y = min(self.start_y, self.current_y)
        width = abs(self.current_x - self.start_x)
        height = abs(self.current_y - self.start_y)
        
        # 转换为绝对屏幕坐标
        screen_coords = self._convert_to_screen_coordinates(x, y, width, height)
        
        # 显示绝对屏幕坐标信息（带背景框增强可读性）
        coord_text = f"绝对坐标: ({screen_coords[0]}, {screen_coords[1]}) 尺寸: {screen_coords[2]}×{screen_coords[3]}"
        
        # 创建背景矩形
        text_x = x + width // 2
        text_y = y - 30
        self.canvas.create_rectangle(
            text_x - 150, text_y - 15,
            text_x + 150, text_y + 15,
            fill='black', outline='yellow', width=1,
            tags='coordinates'
        )
        
        # 创建文字
        self.canvas.create_text(
            text_x, text_y,
            text=coord_text,
            fill='yellow',
            font=('Arial', 12, 'bold'),
            tags='coordinates'
        )
    
    def _close_selection(self):
        """关闭选择窗口"""
        try:
            # 关闭放大镜
            if self.magnifier:
                self.magnifier.destroy()
                self.magnifier = None
                
            # 关闭主选择窗口
            if self.root and self.root.winfo_exists():
                self.root.destroy()
        except Exception as e:
            self.logger.error(f"关闭选择窗口时出错: {str(e)}")
    
    def _cancel_selection(self, event=None):
        """取消选择"""
        self.selected_region = None
        
        # 关闭放大镜
        if self.magnifier:
            self.magnifier.destroy()
            self.magnifier = None
            
        self.root.destroy()

def test_region_selector():
    """测试区域选择器"""
    def on_region_selected(x, y, width, height):
        print(f"选择的区域: 位置({x}, {y}), 尺寸({width}×{height})")
        
        # 显示成功消息
        root = tk.Tk()
        root.withdraw()
        messagebox.showinfo(
            "区域选择完成",
            f"已选择区域（绝对屏幕坐标）:\n位置: ({x}, {y})\n尺寸: {width} × {height}\n\n"
            f"此区域的绝对坐标已保存，可用于自动识别多空资金数据。"
        )
        root.destroy()
    
    # 创建主窗口
    root = tk.Tk()
    root.title("区域选择器测试")
    root.geometry("400x200")
    root.configure(bg='#f0f0f0')
    
    # 居中显示
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 400) // 2
    y = (screen_height - 200) // 2
    root.geometry(f"400x200+{x}+{y}")
    
    # 添加说明
    info_label = tk.Label(
        root,
        text="点击下方按钮开始选择多空资金数据区域\n选择完成后坐标将直接填入程序",
        font=('Microsoft YaHei', 12),
        bg='#f0f0f0',
        fg='#2c3e50'
    )
    info_label.pack(pady=30)
    
    # 添加按钮
    test_button = tk.Button(
        root,
        text="🎯 开始选择屏幕区域",
        command=lambda: RegionSelector(on_region_selected).start_selection(),
        font=('Microsoft YaHei', 14, 'bold'),
        bg='#3498db',
        fg='white',
        width=20,
        height=2,
        relief=tk.RAISED,
        bd=3,
        cursor='hand2'
    )
    test_button.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    test_region_selector()