# -*- coding: utf-8 -*-
"""
GUI消息处理和事件处理模块
包含消息队列处理和各种事件回调函数
"""

import tkinter as tk
from tkinter import messagebox, filedialog, scrolledtext
import queue
import logging
from typing import Dict, Any


class GUILogHandler(logging.Handler):
    """GUI日志处理器 - 线程安全版本"""
    
    def __init__(self, text_widget, message_queue=None):
        super().__init__()
        self.text_widget = text_widget
        self.message_queue = message_queue
        
    def emit(self, record):
        """发出日志记录 - 使用消息队列确保线程安全"""
        try:
            msg = self.format(record)
            if self.message_queue:
                # 使用消息队列进行线程安全的日志输出
                self.message_queue.put(("gui_log", msg))
            else:
                # 回退到直接操作（仅在主线程中使用）
                import threading
                if threading.current_thread() == threading.main_thread():
                    self.text_widget.insert(tk.END, msg + '\n')
                    self.text_widget.see(tk.END)
                    self.text_widget.update()
        except Exception:
            pass


class GUIHandlersMixin:
    """GUI消息处理和事件处理Mixin类"""
    
    def check_message_queue(self):
        """检查消息队列"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "status":
                    self.status_var.set(data)
                elif message_type == "progress":
                    self.progress_var.set(data)
                elif message_type == "log":
                    # 直接在GUI中显示日志消息，而不是通过logger
                    self.log_text.insert(tk.END, data + '\n')
                    self.log_text.see(tk.END)
                    self.log_text.update()
                    # 同时也记录到日志系统
                    self.logger.info(data)
                elif message_type == "gui_log":
                    # 直接在GUI中显示日志消息（线程安全）
                    self.log_text.insert(tk.END, data + '\n')
                    self.log_text.see(tk.END)
                    self.log_text.update()
                elif message_type == "result":
                    self.add_result_to_table(data)
                elif message_type == "filtered_results":
                    self.update_table_with_filtered_results(data)
                elif message_type == "enable_save":
                    self.save_btn.config(state=tk.NORMAL)
                elif message_type == "analysis_complete":
                    # 分析完全结束时恢复按钮状态
                    # 检查是否是暂停状态（继续分析完成）还是正常结束/停止
                    if self.is_paused:
                        self.start_btn.config(text="继续分析", state=tk.NORMAL)
                        self.stop_btn.config(state=tk.NORMAL)  # 暂停状态保持停止按钮激活
                        # 暂停状态不重置进度条
                    else:
                        self.start_btn.config(text="开始分析", state=tk.NORMAL)
                        self.stop_btn.config(state=tk.DISABLED)  # 只有非暂停状态才禁用停止按钮
                        self.progress_var.set(0)
                elif message_type == "analysis_paused":
                    # 分析暂停时更新按钮状态和文字
                    self.start_btn.config(text="继续分析", state=tk.NORMAL)
                    self.stop_btn.config(state=tk.NORMAL)  # 暂停时保持停止按钮激活
                    # 注意：不重置进度条，保持当前进度显示
                elif message_type == "analysis_stopped_from_pause":
                    # 从暂停状态停止时，确保按钮状态正确恢复
                    self.start_btn.config(text="开始分析", state=tk.NORMAL)
                    self.stop_btn.config(state=tk.DISABLED)
                    self.progress_var.set(0)
                elif message_type == "ocr_test_result":
                    self._handle_ocr_test_result(data)
                elif message_type == "ocr_raw_test_result":
                    self._handle_ocr_raw_test_result(data)
                elif message_type == "ocr_test_error":
                    self._handle_ocr_test_error(data)
                elif message_type == "ocr_test_complete":
                    self._handle_ocr_test_complete()
                elif message_type == "ocr_diagnosis_result":
                    self._handle_ocr_diagnosis_result(data)
                elif message_type == "ocr_diagnosis_error":
                    self._handle_ocr_diagnosis_error(data)
                elif message_type == "fund_ocr_test_result":
                    self._handle_fund_ocr_test_result(data)
                elif message_type == "fund_ocr_test_error":
                    self._handle_fund_ocr_test_error(data)
                elif message_type == "fund_ocr_test_complete":
                    self._handle_fund_ocr_test_complete()
                # 买入信号相关消息处理
                elif message_type in ["buy_signal_monitoring_started", "buy_signal_monitoring_stopped", 
                                    "buy_signal_test_result", "buy_signal_test_error"]:
                    self.handle_buy_signal_messages(message_type, data)
                # 卖出信号相关消息处理  
                elif message_type in ["sell_signal_monitoring_started", "sell_signal_monitoring_stopped", 
                                    "sell_signal_test_result", "sell_signal_test_error"]:
                    self.handle_sell_signal_messages(message_type, data)
                # 统一信号测试消息处理
                elif message_type == "signal_test_result":
                    self.handle_signal_messages(message_type, data)
                elif message_type == "signal_test_error":
                    self.handle_signal_messages(message_type, data)
                    
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.check_message_queue)
    
    def _handle_ocr_test_result(self, result: Dict[str, Any]):
        """处理OCR测试结果"""
        try:
            if result and not result.get('error'):
                # 格式化结果显示
                result_text = "OCR识别结果:\n\n"
                
                # 按类型分组显示
                fund_values = {}
                pct_values = {}
                
                for key, value in result.items():
                    if 'fund_pct' in key:
                        pct_values[key] = value
                    elif 'fund' in key:
                        fund_values[key] = value
                
                if fund_values:
                    result_text += "资金值 (万元):\n"
                    for key, value in fund_values.items():
                        display_name = key.replace('_', ' ').title()
                        result_text += f"  {display_name}: {value}\n"
                    result_text += "\n"
                
                if pct_values:
                    result_text += "百分比值:\n"
                    for key, value in pct_values.items():
                        display_name = key.replace('_', ' ').title()
                        result_text += f"  {display_name}: {value}%\n"
                
                if not fund_values and not pct_values:
                    result_text += "未识别到标准格式的多空资金数据\n"
                    result_text += f"原始结果: {result}"
                
                messagebox.showinfo("OCR测试结果", result_text)
                self.logger.info(f"OCR测试成功: {result}")
                
            elif result.get('error'):
                error_msg = result['error']
                messagebox.showerror("OCR测试失败", f"OCR识别出现错误:\n{error_msg}")
                self.logger.error(f"OCR测试错误: {error_msg}")
            else:
                messagebox.showwarning("OCR测试结果", "未识别到任何多空资金数据\n\n可能原因:\n1. 选择区域没有包含资金数据\n2. 图像质量不够清晰\n3. 文字格式不符合预期\n\n建议:\n1. 重新选择包含资金数据的区域\n2. 运行OCR诊断检查系统状态")
                self.logger.warning("OCR测试未识别到数据")
                
        except Exception as e:
            self.logger.error(f"处理OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理OCR测试结果失败: {str(e)}")
    
    def _handle_ocr_raw_test_result(self, result: Dict[str, Any]):
        """处理原始OCR测试结果"""
        try:
            if result and result.get('success'):
                # 格式化原始识别结果显示
                result_text = "OCR原始识别结果:\n\n"
                
                raw_results = result.get('raw_results', [])
                valid_count = result.get('valid_count', 0)
                total_count = result.get('total_count', 0)
                region = result.get('region', {})
                best_strategy = result.get('best_strategy', '未知')
                best_confidence = result.get('best_confidence', 0.0)
                
                result_text += f"测试区域: [{region.get('x', 0)}, {region.get('y', 0)}, {region.get('width', 0)}, {region.get('height', 0)}]\n"
                result_text += f"识别结果: 共 {total_count} 个结果，有效 {valid_count} 个\n"
                result_text += f"最佳策略: {best_strategy} (置信度: {best_confidence:.3f})\n\n"
                
                if raw_results:
                    # 按策略分组显示
                    strategy_groups = {}
                    for r in raw_results:
                        if 'error' not in r:
                            strategy = r.get('strategy', '未知策略')
                            if strategy not in strategy_groups:
                                strategy_groups[strategy] = []
                            strategy_groups[strategy].append(r)
                    
                    # 显示最佳结果
                    valid_results = [r for r in raw_results if 'error' not in r]
                    if valid_results:
                        best_result = max(valid_results, key=lambda x: x['confidence'])
                        result_text += "=== 📍 最佳识别结果 ===\n"
                        result_text += f"文字: \"{best_result['text']}\"\n"
                        result_text += f"置信度: {best_result['confidence']:.3f}\n"
                        result_text += f"引擎: {best_result['engine']}\n"
                        result_text += f"策略: {best_result['strategy']}\n"
                        result_text += f"位置: {best_result['position']}\n\n"
                    
                    # 按策略显示所有结果
                    result_text += "=== 📋 按策略分组的详细结果 ===\n"
                    for strategy, results in strategy_groups.items():
                        if results:  # 只显示有结果的策略
                            result_text += f"\n--- {strategy} ---\n"
                            for i, r in enumerate(results):
                                result_text += f"{i+1}. [{r['engine']}] \"{r['text']}\" (置信度: {r['confidence']:.3f})\n"
                    
                    # 显示所有错误
                    error_results = [r for r in raw_results if 'error' in r]
                    if error_results:
                        result_text += "\n=== ❌ 识别错误 ===\n"
                        error_strategies = set()
                        for r in error_results:
                            strategy_engine = f"{r.get('strategy', '未知')} + {r['engine']}"
                            if strategy_engine not in error_strategies:
                                result_text += f"• {strategy_engine}: {r['error']}\n"
                                error_strategies.add(strategy_engine)
                
                else:
                    result_text += "❌ 未识别到任何文字内容\n\n"
                    result_text += "可能原因:\n"
                    result_text += "• 选择区域没有包含清晰的文字\n"
                    result_text += "• 图像对比度不够或过度曝光\n"
                    result_text += "• 文字太小、模糊或字体特殊\n"
                    result_text += "• OCR引擎未正确初始化\n\n"
                    result_text += "建议:\n"
                    result_text += "• 重新选择包含清晰文字的区域\n"
                    result_text += "• 确保文字与背景有足够对比度\n"
                    result_text += "• 运行OCR诊断检查系统状态\n"
                
                # 创建窗口显示详细结果
                self._show_ocr_result_window(result_text)
                self.logger.info(f"多策略原始OCR测试成功，识别到 {valid_count} 个有效结果，最佳置信度: {best_confidence:.3f}")
                
            elif result.get('error'):
                error_msg = result['error']
                messagebox.showerror("OCR测试失败", f"OCR识别出现错误:\n{error_msg}")
                self.logger.error(f"原始OCR测试错误: {error_msg}")
            else:
                messagebox.showwarning("OCR测试结果", "OCR测试失败，未获得有效结果")
                self.logger.warning("原始OCR测试未获得有效结果")
                
        except Exception as e:
            self.logger.error(f"处理原始OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理原始OCR测试结果失败: {str(e)}")
    
    def _handle_ocr_test_error(self, error_msg: str):
        """处理OCR测试错误"""
        self.logger.error(f"OCR测试错误: {error_msg}")
        messagebox.showerror("OCR测试失败", f"OCR识别失败:\n{error_msg}\n\n建议:\n1. 检查选择的区域是否包含文字\n2. 运行OCR诊断检查系统状态\n3. 尝试重新启动应用程序")
    
    def _handle_ocr_test_complete(self):
        """处理OCR测试完成"""
        self._update_ocr_status()
    
    def _handle_ocr_diagnosis_result(self, report: str):
        """处理OCR诊断结果"""
        try:
            # 创建一个新窗口显示诊断报告
            diag_window = tk.Toplevel(self.root)
            diag_window.title("OCR诊断报告")
            diag_window.geometry("800x600")
            
            # 创建文本框显示报告
            text_frame = tk.Frame(diag_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            report_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            report_text.pack(fill=tk.BOTH, expand=True)
            report_text.insert(tk.END, report)
            report_text.config(state=tk.DISABLED)
            
            # 按钮框架
            btn_frame = tk.Frame(diag_window)
            btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
            
            # 保存报告按钮
            def save_report():
                filename = filedialog.asksaveasfilename(
                    defaultextension=".txt",
                    filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                    title="保存诊断报告"
                )
                if filename:
                    try:
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(report)
                        messagebox.showinfo("成功", f"诊断报告已保存到: {filename}")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存失败: {str(e)}")
            
            tk.Button(btn_frame, text="保存报告", command=save_report).pack(side=tk.LEFT, padx=(0, 10))
            tk.Button(btn_frame, text="关闭", command=diag_window.destroy).pack(side=tk.RIGHT)
            
            self.logger.info("OCR诊断完成")
            
        except Exception as e:
            self.logger.error(f"显示OCR诊断结果失败: {str(e)}")
            messagebox.showerror("错误", f"显示诊断结果失败: {str(e)}")
    
    def _handle_ocr_diagnosis_error(self, error_msg: str):
        """处理OCR诊断错误"""
        self.logger.error(f"OCR诊断错误: {error_msg}")
        messagebox.showerror("OCR诊断失败", f"OCR诊断过程中发生错误:\n{error_msg}")
    
    def _handle_fund_ocr_test_result(self, result: Dict[str, Any]):
        """处理多空资金OCR测试结果"""
        try:
            if result and result.get('success'):
                fund_values = result.get('fund_values', [])
                region = result.get('region', {})
                best_strategy = result.get('best_strategy', '未知')
                
                # 在日志中显示测试信息
                self.logger.info(f"多空资金OCR测试完成 - 区域: [{region.get('x', 0)}, {region.get('y', 0)}, {region.get('width', 0)}, {region.get('height', 0)}]")
                self.logger.info(f"最佳识别策略: {best_strategy}")
                
                if fund_values:
                    # 格式化数值：正数不显示+号，负数显示-号，不显示%号
                    formatted_values = []
                    for value in fund_values:
                        if value >= 0:
                            formatted_values.append(f"{value:.3f}")
                        else:
                            formatted_values.append(f"{value:.3f}")
                    
                    # 在日志中显示最终识别结果
                    self.logger.info(f"✓ 识别到多空资金数据: {', '.join(formatted_values)}")
                    self.logger.info(f"共识别到 {len(fund_values)} 个有效数值")
                    
                    # 显示简单的成功消息
                    messagebox.showinfo("测试成功", f"识别到 {len(fund_values)} 个多空资金数值\n结果已记录在日志中")
                else:
                    self.logger.warning("❌ 未识别到符合格式的多空资金数据")
                    messagebox.showwarning("测试结果", "未识别到多空资金数据\n\n可能原因:\n• 选择区域没有包含资金数据\n• 数据格式不符合预期(x.xxx%或-x.xxx%)\n• 图像质量不够清晰\n\n建议:\n• 重新选择包含资金数据的区域\n• 确保数据格式正确")
                
            elif result.get('error'):
                error_msg = result['error']
                self.logger.error(f"多空资金OCR测试错误: {error_msg}")
                messagebox.showerror("测试失败", f"OCR识别出现错误:\n{error_msg}")
            else:
                self.logger.warning("多空资金OCR测试未获得有效结果")
                messagebox.showwarning("测试结果", "未识别到多空资金数据\n请检查选择的区域和数据格式")
                
        except Exception as e:
            self.logger.error(f"处理多空资金OCR测试结果失败: {str(e)}")
            messagebox.showerror("错误", f"处理测试结果失败: {str(e)}")
    
    def _handle_fund_ocr_test_error(self, error_msg: str):
        """处理多空资金OCR测试错误"""
        self.logger.error(f"多空资金OCR测试错误: {error_msg}")
        messagebox.showerror("多空资金OCR测试失败", f"多空资金OCR识别失败:\n{error_msg}\n\n建议:\n1. 检查选择的区域是否包含多空资金数据\n2. 确保数据格式为x.xxx%或-x.xxx%\n3. 运行OCR诊断检查系统状态\n4. 尝试重新启动应用程序")
    
    def _handle_fund_ocr_test_complete(self):
        """处理多空资金OCR测试完成"""
        self._update_ocr_status()