# -*- coding: utf-8 -*-
"""
GUI截图操作模块
提供截图保存功能，使用与OCR相同的截图方法
"""

import tkinter as tk
from tkinter import messagebox
import os
import logging
import threading
from datetime import datetime
from PIL import Image
import numpy as np
import cv2

from config import COMPASS_SOFTWARE
from image_processor import ImageProcessor


class GUIScreenshotOperationsMixin:
    """GUI截图操作Mixin类"""
    
    def _ensure_screenshot_processor(self):
        """确保截图处理器已初始化（延迟初始化）"""
        if not hasattr(self, 'screenshot_processor') or self.screenshot_processor is None:
            self.screenshot_processor = ImageProcessor()
        return self.screenshot_processor
        
    def create_screenshots_directory(self):
        """创建screenshots目录"""
        try:
            if not os.path.exists(self.screenshots_dir):
                os.makedirs(self.screenshots_dir)
                self.logger.info(f"创建截图目录: {self.screenshots_dir}")
            return True
        except Exception as e:
            self.logger.error(f"创建截图目录失败: {str(e)}")
            return False
    
    def capture_and_save_screenshots(self):
        """捕获并保存截图"""
        try:
            # 更新状态
            if hasattr(self, 'screenshot_status_var') and self.screenshot_status_var:
                self.screenshot_status_var.set("正在截图...")
            
            # 在后台线程中执行截图
            screenshot_thread = threading.Thread(
                target=self._perform_screenshot_task,
                daemon=True
            )
            screenshot_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动截图任务失败: {str(e)}")
            if hasattr(self, 'screenshot_status_var') and self.screenshot_status_var:
                self.screenshot_status_var.set("截图失败")
            messagebox.showerror("错误", f"启动截图任务失败: {str(e)}")
    
    def _perform_screenshot_task(self):
        """执行截图任务（后台线程）"""
        try:
            # 创建截图目录
            if not self.create_screenshots_directory():
                self._show_screenshot_error("无法创建截图目录")
                return
            
            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 获取区域配置
            ocr_region = COMPASS_SOFTWARE.get('ocr_region', {})
            signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})
            status_region = COMPASS_SOFTWARE.get('status_region', {})
            
            saved_files = []
            
            # 截取多空资金区域
            if ocr_region and all(key in ocr_region for key in ['x', 'y', 'width', 'height']):
                fund_file = self._capture_region_to_file(
                    ocr_region, f"多空资金_{timestamp}.png"
                )
                if fund_file:
                    saved_files.append(fund_file)
            else:
                self.logger.warning("多空资金区域未配置或配置不完整")
            
            # 截取信号区域
            if signal_region and all(key in signal_region for key in ['x', 'y', 'width', 'height']):
                signal_file = self._capture_region_to_file(
                    signal_region, f"信号区域_{timestamp}.png"
                )
                if signal_file:
                    saved_files.append(signal_file)
            else:
                self.logger.warning("信号区域未配置或配置不完整")
            
            # 截取状态区域
            if status_region and all(key in status_region for key in ['x', 'y', 'width', 'height']):
                status_file = self._capture_region_to_file(
                    status_region, f"状态区域_{timestamp}.png"
                )
                if status_file:
                    saved_files.append(status_file)
            else:
                self.logger.warning("状态区域未配置或配置不完整")
            
            # 更新状态并显示结果
            if saved_files:
                self._show_screenshot_success(saved_files)
            else:
                self._show_screenshot_error("没有成功保存任何截图\n请检查区域配置是否正确")
                
        except Exception as e:
            self.logger.error(f"截图任务执行失败: {str(e)}")
            self._show_screenshot_error(f"截图任务执行失败: {str(e)}")
    
    def _capture_region_to_file(self, region_config, filename):
        """
        截取指定区域并保存到文件
        
        Args:
            region_config: 区域配置字典 {'x': int, 'y': int, 'width': int, 'height': int}
            filename: 文件名
            
        Returns:
            保存的文件路径或None
        """
        try:
            x = region_config['x']
            y = region_config['y']
            width = region_config['width']
            height = region_config['height']
            
            # 确保截图处理器已初始化
            screenshot_processor = self._ensure_screenshot_processor()
            
            # 使用ImageProcessor截取区域
            img_array = screenshot_processor.capture_region(x, y, width, height)
            
            if img_array is None:
                self.logger.error(f"截取区域失败: {region_config}")
                return None
            
            # 转换为PIL Image
            img_rgb = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(img_rgb)
            
            # 保存文件
            file_path = os.path.join(self.screenshots_dir, filename)
            pil_image.save(file_path, 'PNG')
            
            self.logger.info(f"截图保存成功: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"保存截图失败 {filename}: {str(e)}")
            return None
    
    def _show_screenshot_success(self, saved_files):
        """显示截图成功消息（主线程）"""
        def show_success():
            if hasattr(self, 'screenshot_status_var') and self.screenshot_status_var:
                self.screenshot_status_var.set(f"截图完成，保存了{len(saved_files)}个文件")
            
            # 构建成功消息
            files_info = "\n".join([f"• {os.path.basename(f)}" for f in saved_files])
            message = f"截图保存成功！\n\n保存位置: {os.path.abspath(self.screenshots_dir)}\n\n保存文件:\n{files_info}"
            
            messagebox.showinfo("截图成功", message)
        
        # 在主线程中显示消息
        self.root.after(0, show_success)
    
    def _show_screenshot_error(self, error_msg):
        """显示截图错误消息（主线程）"""
        def show_error():
            if hasattr(self, 'screenshot_status_var') and self.screenshot_status_var:
                self.screenshot_status_var.set("截图失败")
            
            messagebox.showerror("截图失败", error_msg)
        
        # 在主线程中显示错误
        self.root.after(0, show_error)
    
    def get_screenshot_region_info(self):
        """获取截图区域信息用于显示"""
        ocr_region = COMPASS_SOFTWARE.get('ocr_region', {})
        signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})
        status_region = COMPASS_SOFTWARE.get('status_region', {})
        
        info_parts = []
        
        # 多空资金区域信息
        if ocr_region and all(key in ocr_region for key in ['x', 'y', 'width', 'height']):
            info_parts.append(f"多空资金区域: ({ocr_region['x']}, {ocr_region['y']}, {ocr_region['width']}×{ocr_region['height']})")
        else:
            info_parts.append("多空资金区域: 未配置")
        
        # 信号区域信息
        if signal_region and all(key in signal_region for key in ['x', 'y', 'width', 'height']):
            info_parts.append(f"信号区域: ({signal_region['x']}, {signal_region['y']}, {signal_region['width']}×{signal_region['height']})")
        else:
            info_parts.append("信号区域: 未配置")
        
        # 状态区域信息
        if status_region and all(key in status_region for key in ['x', 'y', 'width', 'height']):
            info_parts.append(f"状态区域: ({status_region['x']}, {status_region['y']}, {status_region['width']}×{status_region['height']})")
        else:
            info_parts.append("状态区域: 未配置")
        
        return " | ".join(info_parts)