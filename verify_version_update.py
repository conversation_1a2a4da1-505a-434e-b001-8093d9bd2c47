#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本更新验证脚本
验证CHANGELOG.md和version.txt是否已正确更新到最新版本
"""

import re
from datetime import datetime

def verify_changelog():
    """验证CHANGELOG.md是否已更新"""
    print("🔍 验证CHANGELOG.md更新状态...")
    
    try:
        with open('CHANGELOG.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查当前版本号
        if 'v2025.08.02.1600' in content:
            print("✅ CHANGELOG.md版本号已更新到v2025.08.02.1600")
        else:
            print("❌ CHANGELOG.md版本号未正确更新")
            return False
        
        # 检查新版本段落是否存在
        if '## [v2025.08.02.1600] - 2025-08-02' in content:
            print("✅ 新版本段落已添加")
        else:
            print("❌ 新版本段落未找到")
            return False
        
        # 检查关键功能描述
        key_features = [
            '颜色识别+OCR混合信号分析系统',
            '智能信号识别回退机制',
            '升级gui_signal_operations.py',
            '识别速度提升200-700倍'
        ]
        
        missing_features = []
        for feature in key_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ 缺少关键功能描述: {missing_features}")
            return False
        else:
            print("✅ 所有关键功能描述已添加")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取CHANGELOG.md失败: {str(e)}")
        return False

def verify_version_txt():
    """验证version.txt是否已更新"""
    print("\n🔍 验证version.txt更新状态...")
    
    try:
        with open('version.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查版本号
        if '版本号: v2025.08.02.1600' in content:
            print("✅ version.txt版本号已更新")
        else:
            print("❌ version.txt版本号未正确更新")
            return False
        
        # 检查描述
        if '颜色识别+OCR混合信号分析系统重大升级版' in content:
            print("✅ 版本描述已更新")
        else:
            print("❌ 版本描述未正确更新")
            return False
        
        # 检查构建时间
        if '2025-08-02 16:00:00' in content:
            print("✅ 构建时间已更新")
        else:
            print("❌ 构建时间未正确更新")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取version.txt失败: {str(e)}")
        return False

def main():
    """主验证流程"""
    print("=" * 60)
    print("📋 版本更新验证")
    print("=" * 60)
    
    changelog_ok = verify_changelog()
    version_txt_ok = verify_version_txt()
    
    print("\n" + "=" * 60)
    if changelog_ok and version_txt_ok:
        print("🎉 版本更新验证成功！")
        print("📄 CHANGELOG.md和version.txt都已正确更新到v2025.08.02.1600")
        print("🚀 新版本记录了颜色识别+OCR混合信号分析系统的重大升级")
    else:
        print("⚠️ 版本更新验证发现问题，请检查上述错误信息")
    print("=" * 60)

if __name__ == '__main__':
    main()