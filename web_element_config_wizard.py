# -*- coding: utf-8 -*-
"""
网页元素配置向导GUI
提供用户友好的界面来配置网页元素选择器和HTML数据结构验证
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import logging
import os
import json
import datetime
from typing import Dict, Optional

from web_element_selector import get_web_element_selector_manager, WebElementSelector
from html_data_parser import create_html_data_parser_manager
from config import WEB_AUTOMATION_CONFIG
import asyncio


class WebElementConfigWizard:
    """网页元素配置向导"""
    
    def __init__(self, parent_window=None, callback=None):
        """
        初始化配置向导
        
        Args:
            parent_window: 父窗口
            callback: 配置完成后的回调函数
        """
        self.logger = logging.getLogger(__name__)
        self.parent_window = parent_window
        self.callback = callback
        self.wizard_window = None
        self.selector_manager = get_web_element_selector_manager()
        self.html_parser_manager = create_html_data_parser_manager()
        
        # 配置状态
        self.is_configuring = False
        self.config_thread = None
        self.selected_elements = {}
        
        # HTML验证状态
        self.is_validating_html = False
        self.html_validation_result = None
        
        # 配置模式：'full' = 完整配置, 'login_only' = 仅登录配置
        self.config_mode = 'full'
        
        # 完整配置步骤
        self.full_config_steps = [
            {"name": "username_input", "desc": "用户名输入框", "status": "待配置"},
            {"name": "password_input", "desc": "密码输入框", "status": "待配置"},
            {"name": "data_table", "desc": "数据表格", "status": "待配置"},
            {"name": "stock_code_column", "desc": "股票代码列", "status": "待配置"},
            {"name": "stock_name_column", "desc": "股票名称列", "status": "待配置"},
            {"name": "xiaocao_jingwang_column", "desc": "小草竞王列", "status": "待配置"},
            {"name": "xiaocao_hongpan_column", "desc": "小草红盘起爆列", "status": "待配置"},
            {"name": "xiaocao_lvpan_column", "desc": "小草绿盘低吸列", "status": "待配置"},
            {"name": "xiaocao_lianban_column", "desc": "小草连板接力列", "status": "待配置"},
        ]
        
        # 仅登录配置步骤
        self.login_only_config_steps = [
            {"name": "username_input", "desc": "用户名输入框", "status": "待配置"},
            {"name": "password_input", "desc": "密码输入框", "status": "待配置"},
            {"name": "login_button", "desc": "登录按钮", "status": "待配置"},
        ]
        
        # 当前配置步骤（默认为完整配置）
        self.config_steps = self.full_config_steps
    
    def show_wizard(self):
        """显示配置向导窗口"""
        if self.wizard_window:
            self.wizard_window.lift()
            return
        
        self.wizard_window = tk.Toplevel(self.parent_window)
        self.wizard_window.title("网页元素配置向导")
        self.wizard_window.geometry("800x600")
        self.wizard_window.resizable(True, True)
        
        # 设置窗口图标和属性
        self.wizard_window.transient(self.parent_window)
        self.wizard_window.grab_set()
        
        # 创建界面
        self._create_wizard_interface()
        
        # 加载现有配置
        self._load_existing_config()
        
        # 居中显示
        self._center_window()
    
    def _create_wizard_interface(self):
        """创建向导界面"""
        # 主框架
        main_frame = ttk.Frame(self.wizard_window, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="网页元素配置向导", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 说明文字
        desc_text = ("此向导将帮助您配置网页自动化所需的元素选择器。\n"
                    "您可以选择完整配置或仅配置登录信息。")
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.LEFT)
        desc_label.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 配置模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="配置模式", padding="10")
        mode_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 配置模式单选按钮
        self.config_mode_var = tk.StringVar(value='full')
        
        mode_full_rb = ttk.Radiobutton(mode_frame, text="完整配置（包含所有数据列配置）",
                                      variable=self.config_mode_var, value='full',
                                      command=self._on_config_mode_changed)
        mode_full_rb.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        mode_login_rb = ttk.Radiobutton(mode_frame, text="仅登录配置（只配置用户名、密码、登录按钮）",
                                       variable=self.config_mode_var, value='login_only',
                                       command=self._on_config_mode_changed)
        mode_login_rb.grid(row=1, column=0, sticky=tk.W)
        
        # 快速配置按钮区域
        quick_config_frame = ttk.Frame(mode_frame)
        quick_config_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 导入模板按钮
        self.import_template_btn = ttk.Button(quick_config_frame, text="导入模板配置",
                                            command=self.import_template_config, width=15)
        self.import_template_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 导出配置按钮
        self.export_config_btn = ttk.Button(quick_config_frame, text="导出当前配置",
                                          command=self.export_current_config, width=15)
        self.export_config_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 使用默认配置按钮
        self.use_default_btn = ttk.Button(quick_config_frame, text="使用默认配置",
                                        command=self.use_default_config, width=15)
        self.use_default_btn.grid(row=0, column=2)
        
        # 左侧：配置状态列表
        left_frame = ttk.LabelFrame(main_frame, text="配置步骤", padding="10")
        left_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 创建配置步骤列表
        self.steps_tree = ttk.Treeview(left_frame, columns=("status",), show="tree headings", height=12)
        self.steps_tree.heading("#0", text="配置项")
        self.steps_tree.heading("status", text="状态")
        self.steps_tree.column("#0", width=200)
        self.steps_tree.column("status", width=100)
        
        # 添加滚动条
        steps_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=steps_scrollbar.set)
        
        self.steps_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        steps_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 填充配置步骤
        self._populate_steps_tree()
        
        # 右侧：操作和日志
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 操作按钮区域
        buttons_frame = ttk.LabelFrame(right_frame, text="操作", padding="10")
        buttons_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 第一行按钮
        button_row1 = ttk.Frame(buttons_frame)
        button_row1.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 开始配置按钮
        self.start_config_btn = ttk.Button(button_row1, text="开始配置", 
                                          command=self.start_configuration, width=15)
        self.start_config_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 重新配置按钮
        self.reset_config_btn = ttk.Button(button_row1, text="重新配置", 
                                          command=self.reset_configuration, width=15)
        self.reset_config_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 保存配置按钮
        self.save_config_btn = ttk.Button(button_row1, text="保存配置", 
                                         command=self.save_configuration, 
                                         width=15, state=tk.DISABLED)
        self.save_config_btn.grid(row=0, column=2)
        
        # 第二行按钮 - HTML验证功能
        button_row2 = ttk.Frame(buttons_frame)
        button_row2.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # HTML验证按钮
        self.validate_html_btn = ttk.Button(button_row2, text="验证HTML结构", 
                                           command=self.validate_html_structure, width=15)
        self.validate_html_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 测试HTML数据提取按钮
        self.test_extraction_btn = ttk.Button(button_row2, text="测试数据提取", 
                                             command=self.test_data_extraction, width=15)
        self.test_extraction_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 导出测试数据按钮
        self.export_test_btn = ttk.Button(button_row2, text="导出测试数据", 
                                         command=self.export_test_data, 
                                         width=15, state=tk.DISABLED)
        self.export_test_btn.grid(row=0, column=2)
        
        # 状态显示
        status_frame = ttk.LabelFrame(right_frame, text="状态", padding="10")
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="准备就绪", foreground="green")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, 
                                           maximum=len(self.config_steps), length=300)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 配置日志
        log_frame = ttk.LabelFrame(right_frame, text="配置日志", padding="10")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=50)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=4, column=0, columnspan=2, pady=(20, 0))
        
        # 关闭按钮
        close_btn = ttk.Button(bottom_frame, text="关闭", command=self.close_wizard, width=15)
        close_btn.pack(side=tk.RIGHT)
        
        # 配置网格权重
        self.wizard_window.grid_rowconfigure(0, weight=1)
        self.wizard_window.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(3, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        left_frame.grid_rowconfigure(0, weight=1)
        left_frame.grid_columnconfigure(0, weight=1)
        right_frame.grid_rowconfigure(2, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
    
    def _populate_steps_tree(self):
        """填充配置步骤树"""
        # 清空现有内容
        for item in self.steps_tree.get_children():
            self.steps_tree.delete(item)
            
        # 填充当前配置步骤
        for step in self.config_steps:
            item_id = self.steps_tree.insert("", "end", text=step["desc"], 
                                           values=(step["status"],), tags=(step["name"],))
    
    def _on_config_mode_changed(self):
        """配置模式变化处理"""
        new_mode = self.config_mode_var.get()
        if new_mode != self.config_mode:
            self.config_mode = new_mode
            
            # 切换配置步骤
            if self.config_mode == 'login_only':
                self.config_steps = self.login_only_config_steps
                self.log_message("已切换到仅登录配置模式")
            else:
                self.config_steps = self.full_config_steps
                self.log_message("已切换到完整配置模式")
            
            # 重新填充步骤树
            self._populate_steps_tree()
            
            # 更新进度条最大值
            self.progress_bar.config(maximum=len(self.config_steps))
            self.progress_var.set(0)
    
    def import_template_config(self):
        """导入模板配置"""
        try:
            from tkinter import filedialog
            
            # 根据当前模式选择默认模板文件
            if self.config_mode == 'login_only':
                default_file = "web_element_selectors_login_only_template.json"
                dialog_title = "选择登录配置模板文件"
            else:
                default_file = "web_element_selectors_template.json"
                dialog_title = "选择完整配置模板文件"
            
            # 如果默认模板存在，询问是否使用
            import os
            if os.path.exists(default_file):
                result = messagebox.askyesnocancel("导入模板", 
                                                 f"检测到默认模板文件 {default_file}\n\n"
                                                 f"• 点击【是】：使用默认模板\n"
                                                 f"• 点击【否】：选择其他模板文件\n"
                                                 f"• 点击【取消】：取消导入")
                
                if result is None:  # 取消
                    return
                elif result is True:  # 使用默认模板
                    template_file = default_file
                else:  # 选择其他文件
                    template_file = filedialog.askopenfilename(
                        title=dialog_title,
                        filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
                    )
            else:
                # 没有默认模板，直接选择文件
                template_file = filedialog.askopenfilename(
                    title=dialog_title,
                    filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
                )
            
            if not template_file:
                return
            
            # 读取模板文件
            import json
            with open(template_file, 'r', encoding='utf-8') as f:
                template_config = json.load(f)
            
            # 过滤掉注释字段
            filtered_config = {k: v for k, v in template_config.items() 
                             if not k.startswith('//') and not k.startswith('_')}
            
            # 根据当前模式过滤配置
            if self.config_mode == 'login_only':
                login_keys = ['username_input', 'password_input', 'login_button']
                filtered_config = {k: v for k, v in filtered_config.items() if k in login_keys}
            
            # 应用配置
            self.selected_elements = filtered_config
            self._update_steps_status()
            
            # 更新UI状态
            imported_count = len(filtered_config)
            self.progress_var.set(imported_count)
            self.save_config_btn.config(state=tk.NORMAL)
            self.status_label.config(text=f"已导入 {imported_count} 个配置", foreground="green")
            
            self.log_message(f"成功导入模板配置: {template_file}")
            self.log_message(f"导入了 {imported_count} 个元素配置")
            
            messagebox.showinfo("导入成功", f"成功导入了 {imported_count} 个元素配置！")
            
        except Exception as e:
            error_msg = f"导入模板配置失败: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(error_msg)
            messagebox.showerror("导入失败", error_msg)
    
    def export_current_config(self):
        """导出当前配置"""
        try:
            if not self.selected_elements:
                messagebox.showwarning("提示", "没有可导出的配置")
                return
            
            from tkinter import filedialog
            import json
            
            # 生成默认文件名
            mode_suffix = "login_only" if self.config_mode == 'login_only' else "full"
            default_filename = f"web_element_selectors_{mode_suffix}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # 选择保存路径
            export_file = filedialog.asksaveasfilename(
                title="导出配置文件",
                defaultextension=".json",
                initialvalue=default_filename,
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if not export_file:
                return
            
            # 添加配置说明
            export_config = {
                "// 说明": f"网页元素选择器配置文件 - {self.config_mode} 模式",
                "// 导出时间": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "// 配置模式": self.config_mode,
                "// 元素数量": len(self.selected_elements)
            }
            export_config.update(self.selected_elements)
            
            # 保存文件
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, ensure_ascii=False, indent=2)
            
            self.log_message(f"配置已导出到: {export_file}")
            messagebox.showinfo("导出成功", f"配置已成功导出到:\n{export_file}")
            
        except Exception as e:
            error_msg = f"导出配置失败: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(error_msg)
            messagebox.showerror("导出失败", error_msg)
    
    def use_default_config(self):
        """使用默认配置"""
        try:
            # 根据配置模式选择默认配置
            if self.config_mode == 'login_only':
                default_config = {
                    "username_input": "input[type=\"text\"]",
                    "password_input": "input[type=\"password\"]",
                    "login_button": "button[type=\"button\"]"
                }
                config_name = "仅登录配置"
            else:
                default_config = {
                    "username_input": "input[type=\"text\"]",
                    "password_input": "input[type=\"password\"]",
                    "login_button": "button[type=\"button\"]",
                    "data_table": ".ant-table-tbody",
                    "stock_code_column": "td:nth-child(1)",
                    "stock_name_column": "td:nth-child(2)",
                    "xiaocao_jingwang_column": "td:nth-child(3)",
                    "xiaocao_hongpan_column": "td:nth-child(4)",
                    "xiaocao_lvpan_column": "td:nth-child(5)",
                    "xiaocao_lianban_column": "td:nth-child(6)"
                }
                config_name = "完整配置"
            
            # 确认使用默认配置
            result = messagebox.askyesno("确认", 
                                       f"确定要使用默认的{config_name}吗？\n\n"
                                       f"这将替换当前的所有配置。")
            if not result:
                return
            
            # 应用默认配置
            self.selected_elements = default_config
            self._update_steps_status()
            
            # 更新UI状态
            self.progress_var.set(len(default_config))
            self.save_config_btn.config(state=tk.NORMAL)
            self.status_label.config(text=f"已应用默认{config_name}", foreground="green")
            
            self.log_message(f"已应用默认{config_name}")
            self.log_message(f"包含 {len(default_config)} 个元素配置")
            
            messagebox.showinfo("成功", f"已成功应用默认{config_name}！")
            
        except Exception as e:
            error_msg = f"应用默认配置失败: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(error_msg)
            messagebox.showerror("应用失败", error_msg)
    
    def _load_existing_config(self):
        """加载现有配置"""
        try:
            existing_config = self.selector_manager.load_selectors_from_config()
            if existing_config:
                self.selected_elements = existing_config
                self._update_steps_status()
                self.log_message("已加载现有配置")
                self.save_config_btn.config(state=tk.NORMAL)
        except Exception as e:
            self.logger.error(f"加载现有配置失败: {str(e)}")
    
    def _update_steps_status(self):
        """更新配置步骤状态"""
        for item in self.steps_tree.get_children():
            item_tags = self.steps_tree.item(item, "tags")
            if item_tags:
                step_name = item_tags[0]
                if step_name in self.selected_elements:
                    self.steps_tree.item(item, values=("已配置",))
                    self.steps_tree.set(item, "status", "已配置")
    
    def start_configuration(self):
        """开始配置流程"""
        if self.is_configuring:
            messagebox.showwarning("提示", "配置正在进行中，请稍候...")
            return
        
        # 检查已有配置，询问是否跳过
        existing_config = self.selector_manager.load_selectors_from_config()
        skip_configured = False
        
        if existing_config:
            configured_count = len([s for s in self.config_steps 
                                  if s["name"] in existing_config])
            
            if configured_count > 0:
                result = messagebox.askyesnocancel("配置选项", 
                                                 f"检测到已有 {configured_count} 个配置项。\n\n"
                                                 f"• 点击【是】：仅配置未完成的项目\n"
                                                 f"• 点击【否】：重新配置全部项目\n"
                                                 f"• 点击【取消】：取消配置")
                
                if result is None:  # 用户点击取消
                    return
                elif result is True:  # 用户选择跳过已配置项
                    skip_configured = True
        
        # 确认开始配置
        if skip_configured:
            unconfigured_items = [s["desc"] for s in self.config_steps 
                                if s["name"] not in existing_config]
            message = f"将配置以下未完成的项目：\n\n" + "\n".join(f"• {item}" for item in unconfigured_items)
        else:
            message = "将重新配置所有项目。"
        
        result = messagebox.askyesno("确认配置", 
                                   f"{message}\n\n"
                                   "配置过程中会打开浏览器，请按照提示进行操作。\n"
                                   "是否继续？")
        if not result:
            return
        
        self.is_configuring = True
        self.start_config_btn.config(state=tk.DISABLED)
        self.reset_config_btn.config(state=tk.DISABLED)
        
        if skip_configured:
            self.status_label.config(text="正在启动增量配置...", foreground="orange")
            self.log_message("开始增量配置（跳过已配置项）...")
        else:
            self.status_label.config(text="正在启动完整配置...", foreground="orange")
            self.log_message("开始完整配置...")
        
        # 在后台线程中运行配置
        self.config_thread = threading.Thread(
            target=self._run_configuration, 
            args=(skip_configured,), 
            daemon=True
        )
        self.config_thread.start()
    
    def _run_configuration(self, skip_configured=False):
        """运行配置流程（后台线程）"""
        try:
            if skip_configured:
                # 使用增量配置选择器
                result = self._run_incremental_configuration()
            else:
                # 根据配置模式构建配置步骤
                if self.config_mode == 'login_only':
                    # 仅登录配置步骤
                    selector_config_steps = [
                        {
                            'name': 'username_input',
                            'description': '请点击用户名输入框',
                            'hint': '找到页面上的用户名输入框并点击'
                        },
                        {
                            'name': 'password_input', 
                            'description': '请点击密码输入框',
                            'hint': '找到页面上的密码输入框并点击'
                        },
                        {
                            'name': 'login_button',
                            'description': '请点击登录按钮',
                            'hint': '找到页面上的登录按钮并点击'
                        }
                    ]
                else:
                    # 完整配置使用默认步骤（传递None）
                    selector_config_steps = None
                
                # 启动完整的交互式选择，传递配置步骤
                result = self.selector_manager.start_interactive_selection(config_steps=selector_config_steps)
            
            # 更新UI（需要在主线程中执行）
            self.wizard_window.after(0, self._on_configuration_completed, result)
            
        except Exception as e:
            error_msg = f"配置过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.wizard_window.after(0, self._on_configuration_error, error_msg)
    
    def _run_incremental_configuration(self):
        """运行增量配置（只配置未配置的项目）"""
        try:
            # 加载现有配置
            existing_config = self.selector_manager.load_selectors_from_config() or {}
            
            # 找出未配置的项目
            unconfigured_steps = []
            for step in self.config_steps:
                if step["name"] not in existing_config:
                    unconfigured_steps.append({
                        'name': step["name"],
                        'description': f'请点击{step["desc"]}',
                        'hint': f'配置{step["desc"]}的选择器'
                    })
            
            if not unconfigured_steps:
                self.wizard_window.after(0, lambda: self.log_message("所有项目都已配置完成"))
                return existing_config
            
            # 创建增量选择器
            incremental_selector = IncrementalWebElementSelector(
                config_steps=unconfigured_steps,
                existing_config=existing_config
            )
            
            # 运行增量配置
            new_config = incremental_selector.start_selection()
            
            # 合并配置
            if new_config:
                existing_config.update(new_config)
                return existing_config
            else:
                return existing_config
                
        except Exception as e:
            self.logger.error(f"增量配置失败: {str(e)}")
            return None
    
    def _on_configuration_completed(self, result):
        """配置完成回调（主线程）"""
        self.is_configuring = False
        self.start_config_btn.config(state=tk.NORMAL)
        self.reset_config_btn.config(state=tk.NORMAL)
        
        if result:
            self.selected_elements = result
            self.status_label.config(text="配置完成", foreground="green")
            self.progress_var.set(len(result))
            self.save_config_btn.config(state=tk.NORMAL)
            self._update_steps_status()
            self.log_message(f"配置完成！共配置了 {len(result)} 个元素")
            
            # 显示配置结果
            self._show_config_result()
        else:
            self.status_label.config(text="配置失败或取消", foreground="red")
            self.log_message("配置失败或被用户取消")
    
    def _on_configuration_error(self, error_msg):
        """配置错误回调（主线程）"""
        self.is_configuring = False
        self.start_config_btn.config(state=tk.NORMAL)
        self.reset_config_btn.config(state=tk.NORMAL)
        self.status_label.config(text="配置失败", foreground="red")
        self.log_message(error_msg)
        messagebox.showerror("配置失败", error_msg)
    
    def _show_config_result(self):
        """显示配置结果"""
        if not self.selected_elements:
            return
        
        result_text = "配置结果预览:\\n\\n"
        for key, value in self.selected_elements.items():
            result_text += f"{key}: {value}\\n"
        
        self.log_message(result_text)
    
    def reset_configuration(self):
        """重置配置"""
        if self.is_configuring:
            messagebox.showwarning("提示", "配置正在进行中，无法重置")
            return
        
        result = messagebox.askyesno("确认", "确定要重置所有配置吗？这将清除已保存的配置。")
        if result:
            self.selected_elements = {}
            self.progress_var.set(0)
            self.save_config_btn.config(state=tk.DISABLED)
            self.status_label.config(text="已重置", foreground="blue")
            
            # 重置步骤状态
            for item in self.steps_tree.get_children():
                self.steps_tree.item(item, values=("待配置",))
            
            self.log_message("配置已重置")
            
            # 删除配置文件
            try:
                if os.path.exists("web_element_selectors.json"):
                    os.remove("web_element_selectors.json")
            except Exception as e:
                self.logger.error(f"删除配置文件失败: {str(e)}")
    
    def save_configuration(self):
        """保存配置"""
        if not self.selected_elements:
            messagebox.showwarning("提示", "没有可保存的配置")
            return
        
        try:
            # 保存选择器配置
            self.selector_manager.save_selectors_to_config(self.selected_elements)
            
            # 更新主配置文件
            self._update_main_config()
            
            self.status_label.config(text="配置已保存", foreground="green")
            self.log_message("配置已成功保存到文件")
            
            messagebox.showinfo("成功", "配置已保存！现在可以使用自动登录功能了。")
            
            # 调用回调函数
            if self.callback:
                self.callback(self.selected_elements)
                
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("保存失败", error_msg)
    
    def _update_main_config(self):
        """更新主配置文件"""
        # 这里可以实现将选择器配置更新到config.py的逻辑
        # 暂时先保存到单独的JSON文件
        pass
    
    def log_message(self, message):
        """添加日志消息"""
        if self.log_text:
            self.log_text.insert(tk.END, f"{message}\\n")
            self.log_text.see(tk.END)
    
    def _center_window(self):
        """居中显示窗口"""
        self.wizard_window.update_idletasks()
        width = self.wizard_window.winfo_width()
        height = self.wizard_window.winfo_height()
        x = (self.wizard_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.wizard_window.winfo_screenheight() // 2) - (height // 2)
        self.wizard_window.geometry(f"{width}x{height}+{x}+{y}")
    
    def close_wizard(self):
        """关闭向导"""
        if self.is_configuring:
            result = messagebox.askyesno("确认", "配置正在进行中，确定要关闭吗？")
            if not result:
                return
        
        if self.wizard_window:
            self.wizard_window.destroy()
            self.wizard_window = None
    
    def validate_html_structure(self):
        """验证HTML结构"""
        if self.is_validating_html:
            messagebox.showwarning("提示", "HTML验证正在进行中，请稍候...")
            return
        
        # 请求用户提供HTML文件或内容
        result = messagebox.askyesnocancel("HTML验证", 
                                         "请选择HTML数据来源：\n\n"
                                         "• 点击【是】：从文件加载HTML\n"
                                         "• 点击【否】：手动粘贴HTML内容\n"
                                         "• 点击【取消】：取消验证")
        
        if result is None:  # 用户点击取消
            return
        elif result is True:  # 从文件加载
            self._validate_html_from_file()
        else:  # 手动输入
            self._validate_html_from_input()
    
    def _validate_html_from_file(self):
        """从文件验证HTML"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self._run_html_validation(html_content)
            
        except Exception as e:
            error_msg = f"读取HTML文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("文件读取失败", error_msg)
    
    def _validate_html_from_input(self):
        """从用户输入验证HTML"""
        # 创建HTML输入对话框
        input_dialog = HTMLInputDialog(self.wizard_window, self._run_html_validation)
        input_dialog.show()
    
    def _run_html_validation(self, html_content: str):
        """运行HTML验证"""
        if not html_content.strip():
            messagebox.showwarning("提示", "HTML内容为空")
            return
        
        self.is_validating_html = True
        self.validate_html_btn.config(state=tk.DISABLED)
        self.status_label.config(text="正在验证HTML结构...", foreground="orange")
        self.log_message("开始验证HTML结构...")
        
        # 在后台线程中运行验证
        validation_thread = threading.Thread(
            target=self._validate_html_background,
            args=(html_content,),
            daemon=True
        )
        validation_thread.start()
    
    def _validate_html_background(self, html_content: str):
        """后台验证HTML（后台线程）"""
        try:
            # 使用HTML解析器验证结构
            result = self.html_parser_manager.validate_html_structure(html_content)
            
            # 更新UI（需要在主线程中执行）
            self.wizard_window.after(0, self._on_html_validation_completed, result, html_content)
            
        except Exception as e:
            error_msg = f"HTML验证过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.wizard_window.after(0, self._on_html_validation_error, error_msg)
    
    def _on_html_validation_completed(self, result: Dict, html_content: str):
        """HTML验证完成回调（主线程）"""
        self.is_validating_html = False
        self.validate_html_btn.config(state=tk.NORMAL)
        self.html_validation_result = {'result': result, 'html_content': html_content}
        
        if result['is_valid']:
            self.status_label.config(text="HTML结构验证通过", foreground="green")
            self.test_extraction_btn.config(state=tk.NORMAL)
            self.log_message("✅ HTML结构验证通过")
            self.log_message(f"   - 找到表头: {result['header_found']}")
            self.log_message(f"   - 找到数据行: {result['data_rows_found']}")
            self.log_message(f"   - 总列数: {result['total_columns']}")
            self.log_message(f"   - 总行数: {result['total_rows']}")
            self.log_message(f"   - 目标列: {', '.join(result['target_columns_found'])}")
            
            if result['missing_columns']:
                self.log_message(f"   ⚠️ 缺少列: {', '.join(result['missing_columns'])}")
        else:
            self.status_label.config(text="HTML结构验证失败", foreground="red")
            self.log_message("❌ HTML结构验证失败")
            if 'error' in result:
                self.log_message(f"   错误: {result['error']}")
            else:
                self.log_message(f"   - 找到表头: {result.get('header_found', False)}")
                self.log_message(f"   - 找到数据行: {result.get('data_rows_found', False)}")
                if result.get('missing_columns'):
                    self.log_message(f"   - 缺少列: {', '.join(result['missing_columns'])}")
    
    def _on_html_validation_error(self, error_msg: str):
        """HTML验证错误回调（主线程）"""
        self.is_validating_html = False
        self.validate_html_btn.config(state=tk.NORMAL)
        self.status_label.config(text="HTML验证失败", foreground="red")
        self.log_message(error_msg)
        messagebox.showerror("验证失败", error_msg)
    
    def test_data_extraction(self):
        """测试数据提取"""
        if not self.html_validation_result:
            messagebox.showwarning("提示", "请先验证HTML结构")
            return
        
        if not self.html_validation_result['result']['is_valid']:
            messagebox.showwarning("提示", "HTML结构验证未通过，无法提取数据")
            return
        
        self.status_label.config(text="正在提取测试数据...", foreground="orange")
        self.log_message("开始提取测试数据...")
        
        try:
            html_content = self.html_validation_result['html_content']
            stock_data = self.html_parser_manager.parse_stock_data_from_html(html_content)
            
            if stock_data:
                self.status_label.config(text="数据提取成功", foreground="green")
                self.export_test_btn.config(state=tk.NORMAL)
                
                self.log_message(f"✅ 成功提取 {len(stock_data)} 只股票数据")
                
                # 显示前几条数据作为预览
                preview_count = min(3, len(stock_data))
                self.log_message("数据预览:")
                for i, data in enumerate(stock_data[:preview_count]):
                    self.log_message(f"  {i+1}. {data.get('stock_code', 'N/A')} {data.get('stock_name', 'N/A')} - "
                                   f"竞王:{data.get('jingwang', 'N/A')} 连板接力:{data.get('lianban_jieli', 'N/A')}")
                
                if len(stock_data) > preview_count:
                    self.log_message(f"  ... 还有 {len(stock_data) - preview_count} 条数据")
                    
                # 保存提取的数据
                self.html_validation_result['extracted_data'] = stock_data
                
            else:
                self.status_label.config(text="未提取到数据", foreground="red")
                self.log_message("❌ 未提取到任何股票数据")
                
        except Exception as e:
            error_msg = f"数据提取失败: {str(e)}"
            self.logger.error(error_msg)
            self.status_label.config(text="数据提取失败", foreground="red")
            self.log_message(error_msg)
            messagebox.showerror("提取失败", error_msg)
    
    def export_test_data(self):
        """导出测试数据"""
        if not self.html_validation_result or 'extracted_data' not in self.html_validation_result:
            messagebox.showwarning("提示", "没有可导出的测试数据")
            return
        
        # 请求用户选择导出文件路径
        file_path = filedialog.asksaveasfilename(
            title="保存测试数据",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            html_content = self.html_validation_result['html_content']
            success = self.html_parser_manager.export_to_excel(html_content, file_path)
            
            if success:
                self.status_label.config(text="测试数据导出成功", foreground="green")
                self.log_message(f"✅ 测试数据已导出到: {file_path}")
                messagebox.showinfo("导出成功", f"测试数据已成功导出到:\n{file_path}")
            else:
                self.status_label.config(text="测试数据导出失败", foreground="red")
                self.log_message("❌ 测试数据导出失败")
                messagebox.showerror("导出失败", "测试数据导出失败，请检查文件路径和权限")
                
        except Exception as e:
            error_msg = f"导出测试数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.status_label.config(text="测试数据导出失败", foreground="red")
            self.log_message(error_msg)
            messagebox.showerror("导出失败", error_msg)


class IncrementalWebElementSelector:
    """增量网页元素选择器 - 只配置未配置的项目"""
    
    def __init__(self, config_steps: list, existing_config: dict):
        self.config_steps = config_steps
        self.existing_config = existing_config
        self.logger = logging.getLogger(__name__)
    
    def start_selection(self):
        """开始增量选择"""
        try:
            # 创建增量选择器实例
            selector = WebElementSelector()
            
            # 设置只需要配置的步骤
            selector.config_steps = self.config_steps
            
            # 运行选择流程
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(selector.start_selection())
            loop.close()
            
            return result
            
        except Exception as e:
            self.logger.error(f"增量选择失败: {str(e)}")
            return None


class HTMLInputDialog:
    """HTML输入对话框"""
    
    def __init__(self, parent_window, callback):
        """
        初始化HTML输入对话框
        
        Args:
            parent_window: 父窗口
            callback: 回调函数，接收HTML内容字符串
        """
        self.parent_window = parent_window
        self.callback = callback
        self.dialog_window = None
        self.html_text = None
        
    def show(self):
        """显示对话框"""
        if self.dialog_window:
            self.dialog_window.lift()
            return
        
        self.dialog_window = tk.Toplevel(self.parent_window)
        self.dialog_window.title("HTML内容输入")
        self.dialog_window.geometry("800x500")
        self.dialog_window.resizable(True, True)
        
        # 设置窗口属性
        self.dialog_window.transient(self.parent_window)
        self.dialog_window.grab_set()
        
        # 创建界面
        self._create_interface()
        
        # 居中显示
        self._center_window()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog_window, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题和说明
        title_label = ttk.Label(main_frame, text="HTML内容输入", font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        desc_label = ttk.Label(main_frame, 
                              text="请粘贴包含股票数据表格的HTML内容（包括表头和数据行）：")
        desc_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # HTML文本输入框
        text_frame = ttk.LabelFrame(main_frame, text="HTML内容", padding="10")
        text_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        
        self.html_text = scrolledtext.ScrolledText(text_frame, height=20, width=80)
        self.html_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        
        # 验证按钮
        validate_btn = ttk.Button(button_frame, text="验证并提取", 
                                 command=self._validate_and_extract, width=15)
        validate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空", 
                              command=self._clear_content, width=10)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", 
                               command=self._close_dialog, width=10)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 配置网格权重
        self.dialog_window.grid_rowconfigure(0, weight=1)
        self.dialog_window.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
    
    def _validate_and_extract(self):
        """验证并提取HTML内容"""
        html_content = self.html_text.get("1.0", tk.END).strip()
        
        if not html_content:
            messagebox.showwarning("提示", "请输入HTML内容")
            return
        
        # 调用回调函数处理HTML内容
        self.callback(html_content)
        
        # 关闭对话框
        self._close_dialog()
    
    def _clear_content(self):
        """清空内容"""
        result = messagebox.askyesno("确认", "确定要清空所有内容吗？")
        if result:
            self.html_text.delete("1.0", tk.END)
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.dialog_window:
            self.dialog_window.destroy()
            self.dialog_window = None
    
    def _center_window(self):
        """居中显示窗口"""
        self.dialog_window.update_idletasks()
        width = self.dialog_window.winfo_width()
        height = self.dialog_window.winfo_height()
        x = (self.dialog_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog_window.winfo_screenheight() // 2) - (height // 2)
        self.dialog_window.geometry(f"{width}x{height}+{x}+{y}")


def show_web_element_config_wizard(parent_window=None, callback=None):
    """
    显示网页元素配置向导
    
    Args:
        parent_window: 父窗口
        callback: 配置完成后的回调函数
    """
    wizard = WebElementConfigWizard(parent_window, callback)
    wizard.show_wizard()
    return wizard


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    def on_config_completed(config):
        print(f"配置完成: {config}")
    
    wizard = show_web_element_config_wizard(parent_window=root, callback=on_config_completed)
    root.mainloop()