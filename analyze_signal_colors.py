#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票信号颜色分析工具
分析截图中的信号颜色，生成颜色识别配置
"""

import cv2
import numpy as np
import os
import json
from collections import defaultdict

def analyze_image_colors(image_path):
    """分析单张图片的主要颜色"""
    img = cv2.imread(image_path)
    if img is None:
        return None
    
    # 转换到HSV色彩空间
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # 去除黑色背景 (V < 50)
    mask = hsv[:,:,2] > 50
    non_black_pixels = hsv[mask]
    
    if len(non_black_pixels) == 0:
        return None
    
    # 使用k-means聚类找到主要颜色
    pixels = non_black_pixels.reshape(-1, 3).astype(np.float32)
    
    # 如果像素太少，直接计算平均值
    if len(pixels) < 10:
        avg_hsv = np.mean(pixels, axis=0)
    else:
        # k-means聚类找主色
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(pixels, 1, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        avg_hsv = centers[0]
    
    # 转换回BGR用于显示
    avg_hsv_uint8 = np.uint8([[avg_hsv]])
    avg_bgr = cv2.cvtColor(avg_hsv_uint8, cv2.COLOR_HSV2BGR)[0,0]
    
    return {
        'hsv': tuple(map(int, avg_hsv)),
        'bgr': tuple(map(int, avg_bgr)),
        'rgb': (int(avg_bgr[2]), int(avg_bgr[1]), int(avg_bgr[0])),
        'pixel_count': len(non_black_pixels)
    }

def classify_signal_type(filename):
    """根据文件名分类信号类型"""
    name = filename.lower()
    if name.startswith('kc'):
        return 'open'  # 开仓
    elif name.startswith('qc'):
        return 'close'  # 清仓
    elif name.startswith('cc'):
        return 'hold'   # 持仓
    elif name.startswith('kk'):
        return 'empty'  # 空仓
    else:
        return 'unknown'

def main():
    """主函数"""
    screenshots_dir = 'screenshots'
    
    # 要分析的图片文件
    target_files = ['cc.png', 'cc1.png', 'kc.png', 'kc1.png', 
                   'kk.png', 'kk1.png', 'qc.png', 'qc1.png']
    
    results = {}
    signal_colors = defaultdict(list)
    
    print("=== 股票信号颜色分析 ===\n")
    
    for filename in target_files:
        filepath = os.path.join(screenshots_dir, filename)
        if not os.path.exists(filepath):
            print(f"文件不存在: {filename}")
            continue
            
        color_data = analyze_image_colors(filepath)
        if color_data is None:
            print(f"无法分析: {filename}")
            continue
            
        signal_type = classify_signal_type(filename)
        results[filename] = {
            'signal_type': signal_type,
            'color_data': color_data
        }
        
        signal_colors[signal_type].append(color_data)
        
        print(f"{filename:10s} [{signal_type:5s}]: HSV{str(color_data['hsv']):15s} RGB{str(color_data['rgb']):15s} 像素数:{color_data['pixel_count']}")
    
    print("\n=== 各信号类型颜色统计 ===\n")
    
    # 计算每种信号的平均颜色
    signal_averages = {}
    for signal_type, colors in signal_colors.items():
        if not colors:
            continue
            
        avg_h = np.mean([c['hsv'][0] for c in colors])
        avg_s = np.mean([c['hsv'][1] for c in colors])
        avg_v = np.mean([c['hsv'][2] for c in colors])
        
        avg_rgb = tuple(map(int, np.mean([c['rgb'] for c in colors], axis=0)))
        
        signal_averages[signal_type] = {
            'hsv_avg': (int(avg_h), int(avg_s), int(avg_v)),
            'rgb_avg': avg_rgb,
            'sample_count': len(colors)
        }
        
        signal_names = {'open': '开仓', 'close': '清仓', 'hold': '持仓', 'empty': '空仓'}
        print(f"{signal_names.get(signal_type, signal_type):4s}: HSV{str(signal_averages[signal_type]['hsv_avg']):15s} RGB{str(avg_rgb):15s} (样本数:{len(colors)})")
    
    # 生成颜色识别配置
    color_ranges = {}
    
    # 根据分析结果设置颜色范围
    if 'open' in signal_averages:  # 开仓 - 红色
        color_ranges['open'] = [
            {'lower': [0, 60, 60], 'upper': [10, 255, 255]},
            {'lower': [160, 60, 60], 'upper': [180, 255, 255]}
        ]
    
    if 'close' in signal_averages:  # 清仓 - 绿色
        color_ranges['close'] = [
            {'lower': [40, 60, 60], 'upper': [90, 255, 255]}
        ]
    
    if 'hold' in signal_averages:  # 持仓 - 橙色
        color_ranges['hold'] = [
            {'lower': [10, 60, 60], 'upper': [40, 255, 255]}
        ]
    
    if 'empty' in signal_averages:  # 空仓 - 灰白色
        color_ranges['empty'] = [
            {'lower': [0, 0, 60], 'upper': [180, 30, 255]}
        ]
    
    # 保存配置文件
    config = {
        'color_analysis': results,
        'signal_averages': signal_averages,
        'color_ranges': color_ranges,
        'settings': {
            'bg_threshold': 50,
            'min_pixels': 30
        }
    }
    
    with open('signal_color_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"\n配置已保存到: signal_color_config.json")
    
    return config

if __name__ == '__main__':
    main()