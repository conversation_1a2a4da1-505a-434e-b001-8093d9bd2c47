# -*- coding: utf-8 -*-
"""
统一信号监控模块
继承基础监控类，通过配置参数实现买入和卖出信号的统一监控逻辑
支持自定义关注信号、通知方式和统计逻辑
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any

from base_signal_monitor import BaseSignalMonitor
from dingtalk_notifier import create_dingtalk_notifier_from_config


class UnifiedSignalMonitor(BaseSignalMonitor):
    """统一信号监控器"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any], signal_type: str = 'buy'):
        """
        初始化统一信号监控器
        
        Args:
            compass_automator: 指南针自动化器实例
            signal_analyzer: 信号分析器实例
            config: 监控配置
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        super().__init__(compass_automator, signal_analyzer, config)
        
        self.signal_type = signal_type
        self.signal_type_config = config.get('signal_type_configs', {}).get(signal_type, {})
        
        # 获取关注的主要信号
        self.primary_signal = self.signal_type_config.get('primary_signal', '开' if signal_type == 'buy' else '清')
        self.alert_signals = self.signal_type_config.get('alert_signals', [self.primary_signal])
        
        # 初始化钉钉通知器
        self.dingtalk_notifier = create_dingtalk_notifier_from_config()
        if self.dingtalk_notifier:
            self.logger.info("钉钉通知器初始化成功")
        else:
            self.logger.warning("钉钉通知器初始化失败，将跳过钉钉通知功能")
        
        self.logger.info(f"{self.get_signal_type()}信号监控器初始化完成，主要关注: {self.get_full_signal_name(self.primary_signal)}")
    
    def get_signal_type(self) -> str:
        """获取信号类型名称"""
        return "买入" if self.signal_type == 'buy' else "卖出"
    
    def get_full_signal_name(self, short_signal: str) -> str:
        """获取完整的信号名称"""
        if hasattr(self.signal_analyzer, 'get_full_signal_name'):
            return self.signal_analyzer.get_full_signal_name(short_signal)
        # 回退方案：简单映射
        mapping = {'持': '持仓', '开': '开仓', '空': '空仓', '清': '清仓'}
        return mapping.get(short_signal, short_signal)
    
    def _process_single_stock(self, stock_code: str, message_queue) -> bool:
        """
        处理单个股票的信号识别（统一处理）
        
        Args:
            stock_code: 股票代码
            message_queue: 消息队列
            
        Returns:
            是否处理成功
        """
        try:
            # 搜索股票（包含页面加载检测）
            search_success = self.compass_automator.search_stock(stock_code, wait_for_load=True)
            if not search_success:
                message_queue.put(("log", f"❌ 股票 {stock_code} 搜索失败，跳过"))
                return False

            # 页面加载检测已在search_stock中完成，这里添加额外的等待时间（如果需要）
            additional_wait = self.config.get('page_switch_wait', 1.0)
            if additional_wait > 0:
                message_queue.put(("log", f"  ⏱️ 额外等待 {additional_wait} 秒确保页面稳定"))
                time.sleep(additional_wait)
            
            # 重置鼠标位置到基准位置（确保截图时鼠标在正确位置）
            try:
                if self._move_mouse_to_target_position():
                    message_queue.put(("log", f"  ✓ 鼠标已重置到基准位置"))
                else:
                    message_queue.put(("log", f"  ⚠ 鼠标位置重置失败或未设置基准位置"))
            except Exception as e:
                self.logger.error(f"重置鼠标位置时出错: {str(e)}")
                message_queue.put(("log", f"  ⚠ 鼠标位置重置异常: {str(e)}"))
            
            # OCR识别信号
            signal = self.signal_analyzer.recognize_signal(stock_code)
            if not signal:
                message_queue.put(("log", f"⚠️ 股票 {stock_code} 信号识别失败，跳过"))
                return False
            
            # 记录和检测信号变化
            changed, change_info = self._record_signal_state(stock_code, signal)
            
            if changed:
                self.monitoring_stats['total_signal_changes'] += 1
                
                if change_info['is_first_record']:
                    self._handle_first_signal_detection(stock_code, signal, message_queue)
                else:
                    self._handle_signal_change(stock_code, change_info, message_queue)
            else:
                # 仅在调试模式下记录无变化的情况
                if self.config.get('log_all_signals', False):
                    full_signal_name = self.get_full_signal_name(signal)
                    message_queue.put(("log", f"📊 股票 {stock_code} 信号保持: {full_signal_name}"))
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理股票 {stock_code} 失败: {str(e)}")
            return False
    
    def _handle_first_signal_detection(self, stock_code: str, signal: str, message_queue):
        """处理首次信号检测"""
        full_signal_name = self.get_full_signal_name(signal)
        if signal in self.alert_signals:
            if self.signal_type == 'buy':
                if signal == self.primary_signal:  # 开仓信号
                    message_queue.put(("log", f"💡 股票 {stock_code} 首次记录到{full_signal_name}信号！"))
                    self._send_buy_signal_notification(stock_code, signal, "首次检测")
                elif signal == '清':  # 清仓信号 - 买入监控中的清仓警告
                    message_queue.put(("log", f"⚠️⚠️ 股票 {stock_code} 首次记录到{full_signal_name}信号！！警告提醒！！ ⚠️⚠️"))
                    self._send_buy_signal_notification(stock_code, signal, "首次检测")
                else:
                    message_queue.put(("log", f"📢 股票 {stock_code} 首次记录到{full_signal_name}信号！"))
                    self._send_buy_signal_notification(stock_code, signal, "首次检测")
            else:  # sell
                message_queue.put(("log", f"🚨 股票 {stock_code} 首次记录到{full_signal_name}信号！！危险提醒！！"))
                self._send_sell_signal_notification(stock_code, signal, "首次检测")
        else:
            if self.signal_type == 'sell':
                message_queue.put(("log", f"📝 股票 {stock_code} 首次记录信号: {full_signal_name} (静默记录)"))
            else:
                message_queue.put(("log", f"📝 股票 {stock_code} 首次记录信号: {full_signal_name}"))
    
    def _handle_signal_change(self, stock_code: str, change_info: Dict[str, Any], message_queue):
        """处理信号变化"""
        old_signal = change_info['old_signal']
        new_signal = change_info['new_signal']
        
        old_full_name = self.get_full_signal_name(old_signal)
        new_full_name = self.get_full_signal_name(new_signal)
        
        if new_signal == self.primary_signal:
            if self.signal_type == 'buy':
                message_queue.put(("log", f"🚀 股票 {stock_code} 信号变化: {old_full_name} → {new_full_name} ({new_full_name}信号！)"))
                self._send_buy_signal_notification(stock_code, new_signal, "信号变化", old_signal)
            else:  # sell
                message_queue.put(("log", f"🚨 股票 {stock_code} 信号变化: {old_full_name} → {new_full_name} ({new_full_name}信号！！危险！！)"))
                self._send_sell_signal_notification(stock_code, new_signal, "信号变化", old_signal, is_danger=True)
        elif self.signal_type == 'sell' and old_signal == '持' and new_signal == '清':
            # 特别处理：从持仓变为清仓的情况
            message_queue.put(("log", f"⚠️⚠️ 股票 {stock_code} 持仓股票发出卖出信号！！{old_full_name} → {new_full_name} ⚠️⚠️"))
            self._send_sell_signal_notification(stock_code, new_signal, "持仓转清仓", old_signal, is_danger=True)
        else:
            message_queue.put(("log", f"🔄 股票 {stock_code} 信号变化: {old_full_name} → {new_full_name}"))
            # 其他信号变化的通知
            if self.signal_type == 'buy':
                self._send_buy_signal_notification(stock_code, new_signal, "信号变化", old_signal)
            else:
                self._send_sell_signal_notification(stock_code, new_signal, "信号变化", old_signal)
    
    def _send_buy_signal_notification(self, stock_code: str, signal: str, change_type: str, old_signal: str = None):
        """发送买入信号通知"""
        if self.dingtalk_notifier:
            try:
                self.dingtalk_notifier.send_stock_signal_notification(
                    stock_code=stock_code,
                    signal=signal,
                    change_type=change_type,
                    old_signal=old_signal
                )
            except Exception as e:
                self.logger.error(f"发送钉钉通知失败: {str(e)}")
    
    def _send_sell_signal_notification(self, stock_code: str, signal: str, change_type: str, 
                                     old_signal: str = None, is_danger: bool = False):
        """发送卖出信号通知"""
        if self.dingtalk_notifier:
            try:
                self.dingtalk_notifier.send_sell_signal_notification(
                    stock_code=stock_code,
                    signal=signal,
                    change_type=change_type,
                    old_signal=old_signal,
                    is_danger=is_danger
                )
            except Exception as e:
                self.logger.error(f"发送钉钉通知失败: {str(e)}")
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            统计信息字典
        """
        stats = super().get_monitoring_stats()
        
        if self.signal_type == 'buy':
            # 计算开仓信号的股票数量
            stats['stocks_with_open_signal'] = len([
                code for code, state in self.signal_states.items()
                if state['current_signal'] == '开'
            ])
        else:  # sell
            # 计算清仓信号的股票数量
            stats['stocks_with_sell_signal'] = len([
                code for code, state in self.signal_states.items()
                if state['current_signal'] == '清'
            ])
            
            # 计算从持仓变为清仓的股票数量
            stats['stocks_position_to_sell'] = len([
                code for code, state in self.signal_states.items()
                if (state.get('first_signal') == '持' and 
                    state.get('current_signal') == '清')
            ])
        
        return stats


# 为了向后兼容，创建买入和卖出信号监控器的包装类
class BuySignalMonitor(UnifiedSignalMonitor):
    """买入信号监控器（向后兼容包装）"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any]):
        super().__init__(compass_automator, signal_analyzer, config, signal_type='buy')


class SellSignalMonitor(UnifiedSignalMonitor):
    """卖出信号监控器（向后兼容包装）"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any]):
        super().__init__(compass_automator, signal_analyzer, config, signal_type='sell')